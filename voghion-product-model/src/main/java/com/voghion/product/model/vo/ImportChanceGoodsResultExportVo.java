package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ImportChanceGoodsResultExportVo implements Serializable {
    private static final long serialVersionUID = 1941014977434319736L;

    @ExcelProperty("商品id")
    private Long goodsId;

    @ExcelProperty("导入结果")
    private String result;

}
