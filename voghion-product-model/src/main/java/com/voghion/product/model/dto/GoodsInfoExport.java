package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class GoodsInfoExport implements Serializable {

    @ExcelProperty(value = "商品id", index = 0)
    private String goodsId;

    @ExcelProperty(value = "商品名称", index = 1)
    private String GoodsName;

    @ExcelProperty(value = "店铺id", index = 2)
    private String shopId;

    @ExcelProperty(value = "店铺名称", index = 3)
    private String shopName;

    @ExcelProperty(value = "一级类目id", index = 4)
    private String CategoryLevel1ID;

    @ExcelProperty(value = "一级类目名称", index = 5)
    private String CategoryLevel1Name;

    @ExcelProperty(value = "二级类目id", index = 6)
    private String CategoryLevel2ID;

    @ExcelProperty(value = "二级类目名称", index = 7)
    private String CategoryLevel2Name;

    @ExcelProperty(value = "三级类目id", index = 8)
    private String CategoryLevel3ID;

    @ExcelProperty(value = "三级类目名称", index = 9)
    private String CategoryLevel3Name;

    @ExcelProperty(value = "最小价格", index = 10)
    private BigDecimal minPrice;

    @ExcelProperty(value = "最大价格", index = 11)
    private BigDecimal maxPrice;

    @ExcelProperty(value = "老国家运费", index = 12)
    private BigDecimal oldPrice;

    @ExcelProperty(value = "新国家运费", index = 13)
    private BigDecimal newPrice;

}
