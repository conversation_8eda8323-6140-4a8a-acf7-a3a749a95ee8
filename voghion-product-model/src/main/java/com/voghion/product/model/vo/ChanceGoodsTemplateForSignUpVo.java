package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsTemplateForSignUpVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    /**
     * 模板id
     */
    private Long id;

    @ApiModelProperty("模板商品id")
    private Long goodsId;

    @ApiModelProperty("商品主图")
    private String mainImage;

    @ApiModelProperty("商品名称")
    private String goodsName;

    private Long categoryId;

    @ApiModelProperty("类目名称")
    private String categoryPath;

    @ApiModelProperty("建议价格最低价")
    private BigDecimal minPrice;

    @ApiModelProperty("建议价格最高价")
    private BigDecimal maxPrice;

    /**
     * 跟卖热度 (非模板热度)
     */
    @ApiModelProperty("跟卖热度")
    private Integer hot;

    @ApiModelProperty("近7日销量")
    private Integer sevenSales;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("来源类型 99 其他（1直接创建或者2克隆），3速卖通爬虫，4shein爬虫 5 temu美站  6 temu德站")
    private Integer sourceType;

    /**
     * 生成商品数
     */
    private Integer generateGoodsCount;

}
