package com.voghion.product.model.dto;

import com.colorlight.base.model.dto.BaseDTO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.voghion.product.model.vo.property.GoodsPropertyImgRelevantVo;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 *  <AUTHOR>
 *  @time 2021/6/18 21:57
 *  @describe 
*/
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
public class PropertyValueDTO extends BaseDTO {
    private static final long serialVersionUID = 6852341705783195094L;
    private Long id;

    /**
     * 商品规格关联表 id
     */
    private Long goodsPropertyRelevantId;

    /**
     * 属性值
     */
    private String value;

    private String imgUrl;

    private String mainUrl;

    private List<GoodsPropertyImgRelevantVo> imgRelevantList;

    private Long imgId;

    /**
     * 原始属性值
     */
    private String orginalValue;


    /**
     * 原始属性值
     */
    private String originalValue;

    private Integer sort;

    /**
     * 别名
     */
    private String aliasName;

    private List<Long> categoryIds;

    private List<Long> propertyIds;


    public PropertyValueDTO(String value) {
        this.value = value;
    }
}
