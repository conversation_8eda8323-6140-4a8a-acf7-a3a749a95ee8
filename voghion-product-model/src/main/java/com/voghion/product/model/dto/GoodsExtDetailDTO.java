package com.voghion.product.model.dto;

import com.colorlight.base.model.PageView;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/8 10:41
 * @describe
 */
@Data
public class GoodsExtDetailDTO extends PageView {

    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    private List<Long> goodsIds;

    /**
     * 重量字段
     */
    private String weight;

    /**
     * 体积
     */
    private String packageSize;

    /**
     * 商品规格描述
     */
    private String specs;

    private String channel;

    private String goodsUrl;

    @ApiModelProperty("商品采购链接")
    private String costUrl;

    private String shopName;

    private String shopUrl;

    private List<String> shopUrls;

    private LocalDateTime createTime;

    /**
     * 总评论数（3-50条之间随机生成）
     */
    private Integer commentNumber;

    /**
     * 论分
     */
    private Double score;

    /**
     * 生产周期(单位天)
     */
    private Integer leadTime;

    /**
     * 店铺id 0为KFBUY店铺
     */
    private Long storeId;

    /**
     * 处理时间，格式 1-3
     */
    private String dealTime;

    /**
     * 配送时间，格式 4-3
     */
    private String deliveryTime;

    /**
     * 处理时间，格式 1-3
     */
    private String deliveryCountry;

    /**
     * 原始店铺名称
     */
    private String originalShopName;

    /**
     * 原始商品规格描述
     */
    private String originalSpecs;

    /**
     * 是否已翻译，0:未翻译，1:已翻译
     */
    private Integer translated;

    /**
     * 开始时间
     */
    private String startTime;

    /**
     * 结束时间
     */
    private String endTime;

    private String itemCode;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 所属小二
     */
    private String principal;

    @ApiModelProperty("采购供应商")
    private String procureSupplier;
}
