package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.voghion.product.model.po.GoodsExtraProperty;
import com.voghion.product.model.vo.PropertyDetailInfoVO;
import com.voghion.product.model.vo.PropertyGoodsInfoVO;
import lombok.Data;

import java.util.List;

/**
 * <p>
 * 商品附属表
 * </p>
 *
 * <AUTHOR> @since 2023-01-10
 */
@Data
public class GoodsExtraDTO {

    private static final long serialVersionUID = 1L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 参考链接
     */
    private String referenceUrl;


    private String goodsIdsStr;

    /**
     * 自定义属性Json
     */
    private String propertyJson;




}
