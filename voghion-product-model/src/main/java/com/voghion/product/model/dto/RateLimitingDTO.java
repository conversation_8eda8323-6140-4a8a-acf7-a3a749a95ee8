package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class RateLimitingDTO implements Serializable {
    private static final long serialVersionUID =  3359L;

    private Long shopId;

    private String reason;

    private Long goodsId;
    //限流操作对象 1 店铺 2 商品
    private Integer type;

    private Long version;


    /**
     *封禁天数
     */
    private Integer days;

    //操作类型 1 限流 2 禁售
    private Integer status;


}
