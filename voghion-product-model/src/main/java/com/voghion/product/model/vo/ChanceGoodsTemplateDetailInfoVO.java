package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2021/6/10 18:02
 * @describe
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class ChanceGoodsTemplateDetailInfoVO extends GoodsDetailInfoVO implements Serializable {
    private static final long serialVersionUID = 1874449301139852785L;

    @ApiModelProperty("商品来源 1直接创建，2复制商品")
    private Integer sourceType;
}
