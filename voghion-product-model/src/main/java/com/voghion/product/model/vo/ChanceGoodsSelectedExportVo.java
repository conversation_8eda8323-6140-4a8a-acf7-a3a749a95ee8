package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class ChanceGoodsSelectedExportVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    private Long id;

    @ExcelProperty(value = "机会商品模板id", index = 0)
    private Long templateId;

    @ExcelProperty(value = "模板商品名称", index = 1)
    private String templateGoodsName;

    @ExcelProperty(value = "模板商品主图", index = 2)
    private String templateMainImage;

    private Long shopId;

    @ExcelProperty(value = "商家名称", index = 3)
    private String shopName;

    @ExcelProperty(value = "商品id", index = 4)
    private Long goodsId;

    @ExcelProperty(value = "图片", index = 5)
    private String mainImage;

    @ExcelProperty(value = "商品名称", index = 6)
    private String name;

    private Long categoryId;

    @ExcelProperty(value = "类目", index = 7)
    private String categoryPath;

    private BigDecimal minPrice;

    private BigDecimal maxPrice;

    @ExcelProperty(value = "价格", index = 8)
    private String price;

    @ExcelProperty(value = "国家运费", index = 9)
    private String existGoodsFreight;


    private Integer sourceType;

    @ExcelProperty(value = "来源", index = 10)
    private String sourceTypeStr;

    @ExcelProperty(value = "最新加精人",index = 11)
    private String selectUser;

    @ExcelProperty(value = "最新加精时间",index = 12)
    private Date selectTime;

}
