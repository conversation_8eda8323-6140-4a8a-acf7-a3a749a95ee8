package com.voghion.product.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.tuple.MutableTriple;
import org.apache.commons.lang3.tuple.Triple;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsEditPriceDto implements Serializable {
    private static final long serialVersionUID = 1536514781224338802L;

    private List<SkuChangeDto> skuChangeDtoList;

    private Integer oldTrafficType;
    private Integer newTrafficType;

    private Integer oldIsShow;
    private Integer newIsShow;

    private Double oldWeight;
    private Double newWeight;

    private Integer oldLogisticProperty;
    private Integer newLogisticProperty;

    private String oldCountry;
    private String newCountry;

    private List<FreightChangeDto> freightChangeDtoList;

    /**
     * 批发商品国家运费
     */
    private Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> freightGradientDto;

    public GoodsEditPriceDto(List<SkuChangeDto> skuChangeDtoList) {
        this.skuChangeDtoList = skuChangeDtoList;
    }

    @Data
    @AllArgsConstructor
    public static class SkuChangeDto implements Serializable {
        private static final long serialVersionUID = 3716759358134273863L;

        private Long id;

        private Long skuId;

        private BigDecimal oldPrice;

        private BigDecimal newPrice;

        private BigDecimal oldCostPrice;

        private BigDecimal newCostPrice;

        private Long oldStock;

        private Long newStock;

        private Integer oldSkuStatus;

        private Integer newSkuStatus;

        private BigDecimal oldDefaultDelivery;

        private BigDecimal newDefaultDelivery;

        public SkuChangeDto(Long id, BigDecimal oldPrice, BigDecimal newPrice, Long oldStock, Long newStock) {
            this.id = id;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
            this.oldStock = oldStock;
            this.newStock = newStock;
        }

        public SkuChangeDto(Long id, Long skuId, BigDecimal oldPrice, BigDecimal newPrice){
            this.id = id;
            this.skuId = skuId;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
        }

        public SkuChangeDto(Long id, Long skuId, BigDecimal oldPrice, BigDecimal newPrice, Long oldStock, Long newStock) {
            this.id = id;
            this.skuId = skuId;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
            this.oldStock = oldStock;
            this.newStock = newStock;
        }

        public SkuChangeDto(Long id, BigDecimal oldPrice, BigDecimal newPrice, Long oldStock, Long newStock, BigDecimal oldDefaultDelivery, BigDecimal newDefaultDelivery) {
            this.id = id;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
            this.oldStock = oldStock;
            this.newStock = newStock;
            this.oldDefaultDelivery = oldDefaultDelivery;
            this.newDefaultDelivery = newDefaultDelivery;
        }

        public SkuChangeDto(Long id, BigDecimal oldPrice, BigDecimal newPrice, Long oldStock, Long newStock, Integer oldSkuStatus, Integer newSkuStatus) {
            this.id = id;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
            this.oldStock = oldStock;
            this.newStock = newStock;
            this.oldSkuStatus = oldSkuStatus;
            this.newSkuStatus = newSkuStatus;
        }

        public SkuChangeDto(Long id, BigDecimal oldPrice, BigDecimal newPrice, BigDecimal oldCostPrice, BigDecimal newCostPrice, Long oldStock, Long newStock, Integer oldSkuStatus, Integer newSkuStatus) {
            this.id = id;
            this.oldPrice = oldPrice;
            this.newPrice = newPrice;
            this.oldCostPrice = oldCostPrice;
            this.newCostPrice = newCostPrice;
            this.oldStock = oldStock;
            this.newStock = newStock;
            this.oldSkuStatus = oldSkuStatus;
            this.newSkuStatus = newSkuStatus;
        }
    }

    @Data
    @AllArgsConstructor
    public static class FreightChangeDto implements Serializable {
        private static final long serialVersionUID = 3716759358134273863L;

        private String country;

        private BigDecimal oldFreight;

        private BigDecimal newFreight;

    }
}
