package com.voghion.product.model.dto;

import com.voghion.product.model.enums.CopyGoodsBizMark;
import com.voghion.product.model.enums.GoodsIsShowEnums;
import com.voghion.product.model.enums.GoodsLockLabelTypEnums;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GoodsCopyDto implements Serializable {

    private static final long serialVersionUID = -2293258004365890636L;

    private Long goodsId;

    private Long shopId;

    private String shopName;

    private Integer isLock;

    private GoodsIsShowEnums isShow;

    private GoodsLockLabelTypEnums goodsLockLabelTyp;

    private boolean needTag = false;

    /**
     * 来源类型 1创建机会商品模板 2创建listing商品模板 3商家跟卖listing自动创建商品
     */
    private int type = 0;

    private CopyGoodsBizMark copyGoodsBizMark;
}
