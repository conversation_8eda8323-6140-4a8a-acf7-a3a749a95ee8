package com.voghion.product.model.dto;


import com.voghion.product.model.po.GoodsFreight;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class  GoodsFreightDTO implements Serializable {

    private Boolean IsDefaultDelivery;
    private BigDecimal defaultDelivery;
    private Long goodsId;
    private Map<Long, List<GoodsFreight>> listMap = new HashMap<>();

    private String code;
    /**
     * 当前运费
     */
    private BigDecimal currentFreight;

    /**
     * 降价后国家运费
     * */
    private BigDecimal afterDiscountFreight;

    private String country;
}
