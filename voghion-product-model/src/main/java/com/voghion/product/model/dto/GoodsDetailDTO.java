package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/6/8 21:04
 * @describe
 */
@Data
public class GoodsDetailDTO implements Serializable {
    private Long id;

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商详标题
     */
    private String title;

    /**
     * 商详详情链接（目前商详详情是多张图，";"拼接）
     */
    private String detailUrl;

    /**
     * 详情图片宽度（目前商详详情是多张图，";"拼接）
     */
    private String detailUrlW;

    /**
     * 详情图片高度（目前商详详情是多张图，";"拼接）
     */
    private String detailUrlH;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 商详描述
     */
    private String description;


    private List<Long> ids;

    private List<Long> goodsIds;

    /**
     * 商品尺码图信息
     */
    private String sizeTable;

    /**
     * 尺码图
     */
    private String sizeImage;
}
