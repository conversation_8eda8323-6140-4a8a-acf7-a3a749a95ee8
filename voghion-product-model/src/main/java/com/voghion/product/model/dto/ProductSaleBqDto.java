package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class ProductSaleBqDto implements Serializable {
    private static final long serialVersionUID = -3772105807034041981L;

    private String createDay;
    private String goodsId;
    private Double createTime;
    private Integer isShow;
    private Double latestUnselfTime;
    private String principal;
    private String firstCategoryName;
    private String secondCategoryName;
    private String thirdCategoryName;
    private String shopId;
    private String shopName;
    private String deliveryTypeName;
    private Integer listingId;
    private Integer sort;
    private Double minPrice;
    private Double maxPrice;
    private Integer showPv;
    private Integer clickPv;
    private Integer addPv;
    private Integer orderDetailCnt;
    private Integer dealDetailCnt;
    private Double gmv;
    private Integer showUv;
    private Integer dealUserCnt;
    private Integer dealGoodsNum;
    private Integer realRefundOrderDetailCnt;
    private Double realRefundDetailAmount;
    private Integer qualityRefundCnt;
    private Double qualityRefundAmount;
    private Integer undeliveryRefundCnt;
    private Double undeliveryRefundAmount;
    private Double ecpm;
    private Double averageDetailPrice;
    private Double averageUserPrice;
}
