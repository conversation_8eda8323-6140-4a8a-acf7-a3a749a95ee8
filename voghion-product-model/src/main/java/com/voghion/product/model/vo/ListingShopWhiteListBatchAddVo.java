package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ListingShopWhiteListBatchAddVo implements Serializable {
    private static final long serialVersionUID = 7445131198967986378L;

    @ApiModelProperty(value = "listing id,换行分隔", required = true)
    private String listingIdStr;

    @ApiModelProperty(value = "商家id,换行分隔", required = true)
    private String shopIdStr;
}
