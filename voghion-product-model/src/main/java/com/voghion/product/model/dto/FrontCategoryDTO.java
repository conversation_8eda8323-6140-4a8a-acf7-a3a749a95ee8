package com.voghion.product.model.dto;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/6/3 16:31
 * @describe
 */
@Data
public class FrontCategoryDTO extends BaseDTO {

    /**
     * 分类id
     */
    private Long id;

    /**
     * 操作的前台类目名称
     */
    private String appName;


    /**
     * 分类名称
     */
    private String name;

    /**
     * 跳转类型 4-类目，13-自定义商品列表
     */
    private Integer type;

    /**
     * 数据
     */
    private String value;

    /**
     * 父类ID 0表示当前类目为1级类目
     */
    private Long parentId;

    /**
     * 级别
     */
    private Integer level;

    /**
     * 图标
     */
    private String icon;

    /**
     * 选择图标
     */
    private String selectIcon;

    /**
     * 排序规则，越小越靠前
     */
    private Integer sort;

    /**
     * ids
     */
    private List<Long> ids;

    /**
     * 国别
     */
    private String country;

    /**
     * appChannel
     */
    private String appChannel;
}
