package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
@Data
public class ProhibitionDto implements Serializable {
    private static final long serialVersionUID = -5670094972387333359L;

    /**
     * 禁售/解封id集合
     */
    @ApiModelProperty(value = "禁售/解封id集合,list")
    private List<Long> idList;

    /**
     * 封禁状态 1 解封 2 封禁 3 限流 4 解除限流
     */
    @ApiModelProperty(value = "封禁状态 1 解封 2 封禁 3 限流 4 解除限流 5 加权 6 解除加权")
    private Integer status;
    /**
     * 封禁类型 1 指定天数 2 永久
     */
    @ApiModelProperty(value = "封禁类型 1 指定天数 2 永久")
    private Integer type;

    /**
     *封禁天数
     */
    @ApiModelProperty(value = "封禁天数")
    private Integer days;

    /**
     * 封禁理由枚举id ProhibitionEnums
     */
    @ApiModelProperty(value = "封禁类型，写死5")
    private Integer reasonType;

    /**
     * 封禁理由 ProhibitionEnums
     */
    @ApiModelProperty(value = "封禁理由")
    private String reason;

    /**
     * 操作人名称
     */
    @ApiModelProperty(value = "操作人名称")
    private String operatorName;

    /**
     * 操作者类型 0 人工 1 脚本
     */
    private Integer operatorType=0;
}
