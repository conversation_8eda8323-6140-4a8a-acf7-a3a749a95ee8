package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/10 10:48
 * @describe
 */
@Data
public class GoodsDTO implements Serializable {
    private static final long serialVersionUID = 1472648601374734182L;
    /**
     * 商品id
     */
    private Long goodsId;

    /**
     * 全局商品编号
     */
    private String itemNumber;
    /**
     * 是否已翻译，0:未翻译，1:已翻译
     */
    private Integer translated;
    /**
     * 商品id集合
     */
    private List<Long> goodsIds;
    /**
     * 商品名称
     */
    private String name;


    /**
     * 是否上架 0-下架  1-上架
     */
    private String isShow;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 类目id
     */
    private Long categoryId;

    private List<Long> categoryIds;
    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 商品描述
     */
    private String description;

    /**
     * 渠道
     */
    private String channel;
    /**
     * 原店铺名称
     */
    private String storeName;
    /**
     * 原链接
     */
    private String goodsUrl;

    /**
     * 原店铺链接
     */
    private String shopUrl;

    private List<String> shopUrls;

    /**
     * 尺码图
     */
    private String sizeImage;
    /**
     * 是否全选
     */
    private  Boolean isAll;


    /**
     * 原单词名称
     */
    private String wordName;
    /**
     * 需要替换的单词名称
     */
    private String replaceName;
    /**
     *商品对应的值
     */
    private List<GoodsExtDTO> goodsNames;


    /**
     * 国别
     */
    private String country;

    /**
     * appChannel
     */
    private Integer appChannel;

    /**
     * 起始(最低价)
     */
    private BigDecimal startMinPrice;

    /**
     * 终止(最低价)
     */
    private BigDecimal endMinPrice;

    /**
     * 起始(最高价)
     */
    private BigDecimal startMaxPrice;

    /**
     * 终止(最高价)
     */
    private BigDecimal endMaxPrice;

//    /**
//     * 起始(最低拼团价)
//     */
//    private BigDecimal startMinGrouponPrice;
//
//    /**
//     * 终止(最低拼团价)
//     */
//    private BigDecimal endMinGrouponPrice;
//
//    /**
//     * 起始(最高拼团价)
//     */
//    private BigDecimal startMaxGrouponPrice;
//
//    /**
//     * 终止(最高拼团价)
//     */
//    private BigDecimal endMaxGrouponPrice;

    private String tag;

    private List<String> tags;

    /**
     * 包含标签(存在任意1个)
     */
    private List<Integer> includeTags;

    /**
     * 排除标签
     */
    private List<Integer> excludeTags;

    private Integer updateType;

    private Integer tagType;

    private Integer isDel;

    private Integer status;

    private int pageSize = 10;

    private int pageNow = 1;
    //权重字段
    private Long sortValue;
    //店铺id
    private Long shopId;

    /**
     * 店铺id集合
     */
    private List<Long> shopIds;

    /**
     * not in shopIds
     */
    private List<Long> excludeShopIds;

    private List<Long> excludeGoodsIds;

    //店铺名称
    private String shopName;

    private int goodsSource;

    private Integer isLock;

    /**
     * 供应商商品id
     */
    private String supplierGoodsId;

    /**
     * 来源供应商
     */
    private String originalShopName;

    /**
     * 采购供应商
     */
    private String procureSupplier;

    /**
     * 商品采购链接
     */
    private String costUrl;

    private BigDecimal originalStartPrice;

    private BigDecimal originalEndPrice;

    private Integer isReduction;

    //小二
    private String principal;

    //店铺发货类型
    private List<Integer> deliveryTypes;

    //自营条件...
    //上新人
    private String arrival;

    //首次上架开始时间
    private Date firstShowStartTime;

    //首次上架结束时间
    private Date firstShowEndTime;

    //最后人工修改开始时间
    private Date lastEditStartTime;

    //最后人工修改结束时间
    private Date lastEditEndTime;

    //是否支持备货
    private Integer isSupportStockUp;

    /**
     * 仅竞标商品 1是 0否
     */
    private Integer isOnlyListing;

    /**
     * 锁定标签(投放,测款,热卖,flashDeal,运营选品,满减大促,七日达,精选店铺,机会商品,listing商品)
     */
    private String lockLabel;

    /**
     * 店铺类型 0代发 1直发 2香港代发 3自营 4scm自营
     * @see com.voghion.product.model.enums.DeliveryTypeEnum
     */
    private Integer deliveryType;

    /**
     * 1 - AW网盟 2 - AW网盟PC
     */
    private Integer awType;

    /**
     * 商品类型 1表示单品 2表示组合商品 3批发商品
     */
    private Integer goodsType;

    /**
     * 是否有货源：0无1有
     */
    private Integer hasLeadTime;

    /**
     * 降价空间排序
     * 0降序；1升序
     */
    private Integer reductionAmountSort;

    /**
     * 上新申请开始时间
     */
    private Date selfSupportCreateStartTime;

    /**
     * 上新申请结束时间
     */
    private Date selfSupportCreateEndTime;

    /**
     * 上新状态 0待处理 1已处理 2待分配 -1已驳回
     */
    private Integer selfSupportStatus;

    /**
     * 是否已分配上新 1已分配 0未分配
     */
    private Integer isDistributeArrival;

    /**
     * 排序规则 0创建时间降序(默认) 1上新申请时间升序
     */
    private Integer sortType;

    /**
     * 品牌id
     */
    private Long brandId;
}
