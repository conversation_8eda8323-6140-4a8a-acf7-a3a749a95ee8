package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/5/19 18:48
 */
@Data
public class ItemGoodsFillDto implements Serializable {

    @ApiModelProperty("标签 (多个标签以,号分隔)")
    private String tag;

    @ApiModelProperty("商品原始名称")
    private String originalName;

    @ApiModelProperty("物流标签Id")
    private Long labelId;

    @ApiModelProperty("物流标签名称")
    private String labelName;

    @ApiModelProperty("运费模板主键Id")
    private Long freightTemplateId;

    @ApiModelProperty("原始销量")
    private Long orginalSales;

}
