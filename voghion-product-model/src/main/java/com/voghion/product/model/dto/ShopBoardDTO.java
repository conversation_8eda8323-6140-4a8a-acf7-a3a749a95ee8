package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
@Data
public class ShopBoardDTO implements Serializable {
    private static final long serialVersionUID = 8995351L;
    //创建时间
    private String createDay;
    //近30天平均入库时间
    private Float avgEntryConfirmDays;
    //近30天成交不卖率
    private Float dealUndeliveryRefundRate;
    //近30天质量原因退款率
    private Float qualityRefundRate;
    //近90天好评率
    private Float goodCommentRate;
    //近90天评价均分
    private Float avgScore;
    //近30天支付转化率
    private Float payRate;
    //近30天复购率
    private Float payTwiceRate;
    //店铺活跃度
    private String shopType;
    //星级0-5
    private Integer star;
    //店铺Id
    private Long shopId;

    //180天差评率
    private Float badCommentRate;

    //最低单数
    private String minimunOrderType;

    //30天申诉通过质量原因退款数
    private Integer appealApplyQualityCnt30day;

    // 30天成交退款数
    private Integer dealUndeliveryRefundCnt30day;

    // 90天成交数订单
    private Integer payCnt90;

    //180天差评数
    private Integer badCommentCnt;

    //30天质量原因退款数（未排除申诉通过）
    private Integer qualityRefundCnt30day;

    //30天发货数量
    private Integer deliveryCnt30day;

    //30天收货数量
    private Integer receiveCnt30day;

    //30-60天出库未收货数量（就是那种可能丢件了的）
    private Integer outUnreceiveCnt3060day;

    //成交不卖申诉通过数量
    private Integer appealApplyDealUndeliveryCnt30day;

    //30天动销率
    private Float  dynamicSalesRate;
}

