package com.voghion.product.model.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
@Data
@ApiModel
public class FavoritesDTO {
    /**
     * 自增id
     */
    @ApiModelProperty(value = "活动id")
    private Long id;

    /**
     * 商品收藏夹名称
     */
    @ApiModelProperty(value = "商品收藏夹名称")
    private String favoritesName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remarks;


}
