package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class QueryGoodsBlacklistCondition {
    @ApiModelProperty("商品id集合字符串")
    private String goodsIdListStr;
    @ApiModelProperty("商家名称")
    private String goodsName;
    @ApiModelProperty("商家id")
    private Long shopId;
    @ApiModelProperty("商家名称")
    private String shopName;
    @ApiModelProperty("上下架状态")
    private Integer isShow;
    @ApiModelProperty("开始时间")
    private String startTime;
    @ApiModelProperty("结束时间")
    private String endTime;
    @ApiModelProperty("类目id")
    private Long categoryId;

    private List<Long> categoryIds;
    private List<Long> goodsIdList;
    private Integer pageSize;
    private Integer pageNow;
    @ApiModelProperty("移出黑名单id集合")
    private List<Long> idList;
}
