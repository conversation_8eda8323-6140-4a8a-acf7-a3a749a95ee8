package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description:
 * @date: 2022/12/16 上午11:51
 * @author: jashley
 */
@Data
@ApiModel
public class GoodsPriceReductionNoticeVo implements Serializable {
    private static final long serialVersionUID = -2682878501963900468L;

    private Long id;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String name;

    @ApiModelProperty("商品主图")
    private String mainImage;

    @ApiModelProperty("当前价格")
    private String price;

    @ApiModelProperty("建议价格")
    private String suggestPrice;

    @ApiModelProperty("降价原因")
    private String reductionReason;

    @ApiModelProperty("处罚措施")
    private String punish;

}
