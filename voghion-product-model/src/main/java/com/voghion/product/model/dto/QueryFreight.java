package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.tuple.MutableTriple;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class QueryFreight implements Serializable {
    private static final long serialVersionUID = 591223985630527436L;

    @ApiModelProperty("商品重量")
    private String weight;

    @ApiModelProperty("商品尺寸")
    private PackageSizeDTO packageSize;

    @ApiModelProperty("类目id")
    private Long categoryId;

    @ApiModelProperty("物流属性 1普货 2液体/粉末/膏状 3食品/刀具/电子烟/无人机/小家电/充电宝/枪型玩具 4含电/含磁铁 5香水 6不确定")
    private Integer logisticsProperty;

    @ApiModelProperty("国家(暂兼容海外仓)")
    private List<String> CountryCodes;

    private Long shopId;

    /**
     * 商品物流类型，0-普货，1-特货，2-香水
     */
    private Integer goodsType;

    /**
     * 商品类型 1表示单品 2表示组合商品 3批发商品
     */
    private Integer type;

    private Long goodsId;

    /**
     * 物流场景 1通用 2尾货 3特货(40\45\50标签)
     */
    private Integer logisticsScene = 1;

    /**
     * 梯度运费信息
     */
    private Map<String, List<MutableTriple<BigDecimal, Integer, Integer>>> freightGradientDto;

    /**
     * 包含佣金
     */
    private boolean containCommission = true;
}
