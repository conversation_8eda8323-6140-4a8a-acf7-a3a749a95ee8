package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @time 2021/6/18 21:42
 * @describe
 */
@Data
public class SkuInfoDTO implements Serializable {

    /**
     * skuId
     */
    private Long skuId;

    /**
     * sku价格
     */
    private BigDecimal price;

    /**
     * sku价格
     */
    private BigDecimal costPrice;
    /**
     * sku价格
     */
    private BigDecimal marketPrice;

    /**
     * 拼接属性名称
     */
    private String name;
}
