package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class BuyOnePieceDTO implements Serializable {
    private static final long serialVersionUID = 87333359L;

    @ApiModelProperty("活动名称")
    private String name;

    @ApiModelProperty("商品id")
    private Long goodsId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("skuid")
    private Long skuId;

    @ApiModelProperty("sku规格")
    private String skuName;

    @ApiModelProperty("销售价格（价格+运费）")
    private BigDecimal price;

    @ApiModelProperty("一口价")
    private BigDecimal discountPrice;

    @ApiModelProperty("创建人")
    private String operator;

}
