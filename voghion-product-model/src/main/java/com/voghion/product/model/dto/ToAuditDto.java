package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ToAuditDto implements Serializable {
    private static final long serialVersionUID = 4307595437248433284L;
    /**
     * 店铺Id
     */
    @ApiModelProperty("店铺Id")
    private Long ShopId;

    /**
     * 去审核的商品id
     */
    @ApiModelProperty("去审核的商品id")
    private List<Long> goodsIds;
}
