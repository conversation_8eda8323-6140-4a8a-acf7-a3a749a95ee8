package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class GoodsListInfoDTO implements Serializable {

    @ExcelProperty(value ="本级类目id", index = 0)
    private Long CategoryID;

    @ExcelProperty(value ="本级类目名称", index = 1)
    private String CategoryName;

    @ExcelProperty(value ="本级类目中文名称", index = 2)
    private String CategoryZhName;

    @ExcelProperty(value ="一级类目id", index = 3)
    private Long CategoryLevel1ID;

    @ExcelProperty(value ="一级类目名称", index = 4)
    private String CategoryLevel1Name;

    @ExcelProperty(value ="一级类目中文名称", index = 5)
    private String CategoryLevel1ZhName;

    @ExcelProperty(value ="二级类目id", index = 6)
    private Long CategoryLevel2ID;

    @ExcelProperty(value ="二级类目名称", index = 7)
    private String CategoryLevel2Name;

    @ExcelProperty(value ="二级类目中文名称", index = 8)
    private String CategoryLevel2ZhName;

    @ExcelProperty(value ="三级类目id", index = 9)
    private Long CategoryLevel3ID;

    @ExcelProperty(value ="三级类目名称", index = 10)
    private String CategoryLevel3Name;

    @ExcelProperty(value ="三级类目中文名称", index = 11)
    private String CategoryLevel3ZhName;

    @ExcelProperty(value ="四级类目id", index = 12)
    private Long CategoryLevel4ID;

    @ExcelProperty(value ="四级类目名称", index = 13)
    private String CategoryLevel4Name;

    @ExcelProperty(value ="四级类目中文名称", index = 14)
    private String CategoryLevel4ZhName;

    @ExcelProperty(value ="五级类目id", index = 15)
    private Long CategoryLevel5ID;

    @ExcelProperty(value ="五级类目名称", index = 16)
    private String CategoryLevel5Name;

    @ExcelProperty(value ="五级类目中文名称", index = 17)
    private String CategoryLevel5ZhName;

}
