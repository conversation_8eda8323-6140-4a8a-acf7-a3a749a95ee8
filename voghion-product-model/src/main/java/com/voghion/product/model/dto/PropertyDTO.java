package com.voghion.product.model.dto;

import com.voghion.product.model.po.Property;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/6/8 22:12
 * @describe
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PropertyDTO extends Property {
    private static final long serialVersionUID = 6556955177446581703L;
    /**
     * 类目id集合
     */
    private List<Long> categoryIds;

    private List<PropertyValueDTO> values;

    /**
     * 原始属性名
     */
    private String orginalName;


    private int position;
}
