package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.voghion.product.model.conveter.LocalDateTimeConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
public class TongDunExport implements Serializable {

    /**
     * 店铺id
     */
    @ExcelProperty(value = "商家id", index = 0)
    private Long shopId;

    @ExcelProperty(value = "商家名称", index = 1)
    private String shopName;

    /**
     * 商品id
     */
    @ExcelProperty(value = "商品id", index = 2)
    private Long goodsId;

    /**
     * 商品图片
     */
    @ExcelProperty(value = "商品主图", index = 3)
    private String goodsImage;

    /**
     * 商品名称
     */
    @ExcelProperty(value = "商品标题", index = 4)
    private String goodsName;

    /**
     * 类目名称
     */
    @ApiModelProperty("类目名称")
    @ExcelProperty(value = "类目名称", index = 5)
    private String categoryName;

    @ApiModelProperty("商品状态")
    @ExcelProperty(value = "商品状态", index = 6)
    private String isShow;

    /**
     * 违规详情
     */
    @ApiModelProperty("命中规则")
    @ExcelProperty(value = "命中规则", index = 7)
    private String illegalDetail;

    /**
     * 创建时间
     */
    @ApiModelProperty("同盾驳回时间")
    @ExcelProperty(value = "同盾驳回时间", index = 8)
    private String createTime;

    /**
     * 审核人
     */
    @ApiModelProperty("审核人")
    @ExcelProperty(value = "审核人", index = 9)
    private String operator;

    /**
     * 审核时间
     */
    @ApiModelProperty("审核时间")
    @ExcelProperty(value = "审核时间", index = 10)
    private String aduitTime;


}
