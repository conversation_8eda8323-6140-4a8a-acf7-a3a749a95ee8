package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
@ApiModel
public class ShopBanSaleOrLimitingInfoVO implements Serializable {


    private static final long serialVersionUID = 4104006909420786618L;

    /**
     * 状态 1:已解禁售 99:禁售中 10:已解限流 20:限流中
     */
    @ApiModelProperty("状态 1:已解禁售 99:禁售中 10:已解限流 20:限流中")
    private Integer status;

    /**
     * 原因
     */
    @ApiModelProperty("原因")
    private String reason;

    /**
     * 起始时间
     */
    @ApiModelProperty("起始时间")
    private String createTime;

    /**
     * 禁用天数
     */
    @ApiModelProperty("持续天数")
    private String days;

}
