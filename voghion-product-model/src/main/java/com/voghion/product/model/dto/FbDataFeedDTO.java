package com.voghion.product.model.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 投放导入配置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-20
 */
@Data
public class FbDataFeedDTO implements Serializable {
    private static final long serialVersionUID = 9052485530507049921L;
    @ExcelProperty(value = "id", index = 0)
    private Long id;

    @ExcelProperty(value = "title", index = 1)
    private String title;

    @ExcelProperty(value = "description", index = 2)
    private String description;

    @ExcelProperty(value = "availability", index = 3)
    private String availability;

    @ExcelProperty(value = "condition", index = 4)
    private String condition;

    @ExcelProperty(value = "price", index = 5)
    private String price;

    @ExcelProperty(value = "ios_url", index = 6)
    private String iosUrl;

    @ExcelProperty(value = "ios_app_store_id", index = 7)
    private String iosAppStoreId;

    @ExcelProperty(value = "ios_app_name", index = 8)
    private String iosAppName;

    @ExcelProperty(value = "android_url", index = 9)
    private String androidUrl;

    @ExcelProperty(value = "android_package", index = 10)
    private String androidPackage;

    @ExcelProperty(value = "android_app_name", index = 11)
    private String androidAppName;

    @ExcelProperty(value = "link", index = 12)
    private String link;

    @ExcelProperty(value = "image_link", index = 13)
    private String imageLink;

    @ExcelProperty(value = "brand", index = 14)
    private String brand;

    @ExcelProperty(value = "custom_label_0", index = 15)
    private String customLabel0;

    @ExcelProperty(value = "custom_label_1", index = 16)
    private String customLabel1;

    @ExcelProperty(value = "custom_label_2", index = 17)
    private String customLabel2;

    @ExcelProperty(value = "custom_label_3", index = 18)
    private String customLabel3;

    @ExcelProperty(value = "custom_label_4", index = 19)
    private String customLabel4;

    @ExcelProperty(value = "sale_price", index = 20)
    private String salePrice;

    @ExcelProperty(value = "sale_price_effective_date", index = 21)
    private String salePriceEffectiveDate;

    @ExcelProperty(value = "product_type", index = 22)
    private String productType;

    @ExcelProperty(value = "gender", index = 23)
    private String gender;

    @ExcelProperty(value = "ios_cate_url", index = 24)
    private String iosCateUrl;

    @ExcelProperty(value = "android_cate_url", index = 25)
    private String androidCateUrl;

    @ExcelProperty(value = "video[0].url", index = 26)
    private String video;
}
