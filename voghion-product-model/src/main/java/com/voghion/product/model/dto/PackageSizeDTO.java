package com.voghion.product.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @time 2021/6/18 21:41
 * @describe
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PackageSizeDTO implements Serializable {
    private static final long serialVersionUID = 6410951804391616397L;

    @ApiModelProperty("长")
    private String length;

    @ApiModelProperty("宽")
    private String width;

    @ApiModelProperty("高")
    private String height;

    public PackageSizeDTO(String packageSizeStr) {
        if (StringUtils.isNotBlank(packageSizeStr)) {
            String[] arr = packageSizeStr.split("\\*");
            this.length = arr[0];
            this.width = arr[1];
            this.height = arr[2];
        }
    }

    public PackageSizeDTO(Double length, Double width, Double height) {
        this.length = String.valueOf(length);
        this.width = String.valueOf(width);
        this.height = String.valueOf(height);
    }
}
