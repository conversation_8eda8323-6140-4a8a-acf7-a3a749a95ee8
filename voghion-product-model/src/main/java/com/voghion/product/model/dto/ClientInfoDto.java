package com.voghion.product.model.dto;

import com.alibaba.fastjson.JSONObject;
import com.voghion.product.model.vo.BaseTokenUserInfo;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class ClientInfoDto implements Serializable {
    private static final long serialVersionUID = 2206439021789791912L;
    private List<JSONObject> authFields;

    private String api;

    private List<MenuDto> menu;

    private BaseTokenUserInfo user;

    private List<AuthRoleDto> authRoleList;

    @Data
    public static class MenuDto implements Serializable{
        private static final long serialVersionUID = -1770721081693456336L;
        private List<MenuDto> children;

        private String code;

        private String icon;

        private Long id;

        private String method;

        private String name;

        private Long parentId;

        private String uri;

        private Integer resourceType;

    }

    @Data
    public static class AuthRoleDto implements Serializable {
        private static final long serialVersionUID = -2540703250024674661L;

        private Long id;

        private String name;

        private Integer status;

        private Date createTime;

        private Date updateTime;
    }
}
