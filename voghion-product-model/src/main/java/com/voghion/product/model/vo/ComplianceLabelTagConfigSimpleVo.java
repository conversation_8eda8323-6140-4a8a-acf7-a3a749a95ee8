package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@ApiModel
public class ComplianceLabelTagConfigSimpleVo implements Serializable {
    private static final long serialVersionUID = -1849195103985276693L;

    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("标签id")
    private Integer tagId;
}
