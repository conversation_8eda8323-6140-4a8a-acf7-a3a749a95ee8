package com.voghion.product.model.dto;

import com.voghion.product.model.po.GoodsItem;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2021/6/9 15:32
 * @describe
 */
@Data
public class GoodsItemDTO extends GoodsItem {
    private static final long serialVersionUID = -2074348726487302885L;

    private List<Long> skuIds;

    private List<Long> goodsIds;

    private List<Long> ids;

    private String imageUrl;

    /**
     * 长
     */
    private String length;
    /**
     * 宽
     */
    private String width;
    /**
     * 高
     */
    private String height;

    /**
     * 降价后价格
     * */
    private BigDecimal afterDiscountPrice;

    /**
     * 降价后运费
     * */
    private BigDecimal afterDiscountDelivery;

    private String skuPropertyRelevantStr;

    /**
     * goods_sku表的id
     */
    private Long goodsSkuId;

    /**
     * sku规格map，用作匹配
     */
    private Map<String, String> skuProperty;

}
