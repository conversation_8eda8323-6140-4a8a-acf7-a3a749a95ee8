package com.voghion.product.model.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ImportGoodsAuditTaskUpdateVo implements Serializable {
    private static final long serialVersionUID = 1941014977434319736L;

    @ExcelProperty(value = "商品id")
    private Long goodsId;

    @ExcelProperty(value = "优先级(0代表P0,1代表P1,2代表P2,3代表P3)")
    private Integer level;

    @ExcelProperty(value = "审核人")
    private String auditor;

}
