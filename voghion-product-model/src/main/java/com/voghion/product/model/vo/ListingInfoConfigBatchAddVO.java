package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/15
 */
@Data
@ApiModel
public class ListingInfoConfigBatchAddVO implements Serializable {
    private static final long serialVersionUID = 6876300785945294355L;

    @ApiModelProperty(value = "listing id,换行分隔")
    private String listingIdStr;

    @ApiModelProperty(value = "标签,146、507、514")
    private List<Long> tagIds;

    @ApiModelProperty(value = "经营类型,0-代发商家 1-直发商家 2-香港代发 3-自营店铺 4-自营店铺scm")
    private List<Integer> deliveryTypes;

    @ApiModelProperty(value = "操作类型,0-添加 1-删除")
    private Integer operationType;
}
