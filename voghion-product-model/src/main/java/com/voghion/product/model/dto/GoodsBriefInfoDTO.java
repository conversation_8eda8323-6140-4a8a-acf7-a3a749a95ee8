package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
@Data
public class GoodsBriefInfoDTO  implements Serializable {

    /**
     * 商品ID
     */
    private Long goodsId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 最低销售价格
     */
    private BigDecimal minPrice;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 不加vat的商品minPrice
     */
    private BigDecimal vatBeforeMinPrice;

    /**
     * 加vat的商品minPrice
     */
    private BigDecimal vatBeforeMaxPrice;

    /**
     * 店铺ID
     */
    private Long shopId;

}
