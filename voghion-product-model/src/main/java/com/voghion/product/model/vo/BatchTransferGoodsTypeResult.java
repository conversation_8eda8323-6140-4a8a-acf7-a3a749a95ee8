package com.voghion.product.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
@ApiModel
@NoArgsConstructor
@AllArgsConstructor
public class BatchTransferGoodsTypeResult implements Serializable {
    private static final long serialVersionUID = 3932663637490958723L;

    @ApiModelProperty("转换成功的商品id")
    private List<Long> successGoodsIds;

    @ApiModelProperty("转换失败的商品id及原因")
    private Map<String, List<Long>> failGoodsMap;


}
