package com.voghion.product.model.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2021/5/10 13:53
 * @describe
 */
@Data
public class GoodsInfoDTO implements Serializable {
    private static final long serialVersionUID = 5994082307649786880L;

    private Long id;


    /**
     * 商品名称
     */
    private String name;


    /**
     * 商品编码
     */
    private String code;



    /**
     * 是否上架 0-下架  1-上架
     */
    private String isShow;

    /**
     * 创建时间
     */
    private Date createTime;



    /**
     * 最低销售价格
     */
    private BigDecimal minPrice;

    /**
     * 最低展示价格
     */
    private BigDecimal minMarketPrice;

    /**
     * 最高销售价格
     */
    private BigDecimal maxPrice;

    /**
     * 最高展示价格
     */
    private BigDecimal maxMarketPrice;

    /**
     * 最低拼团价
     */
    private BigDecimal minGrouponPrice;

    /**
     * 最高拼团价
     */
    private BigDecimal maxGrouponPrice;

    /**
     * 商品主图
     */
    private String mainImage;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 商品原链接
     */
    private String goodsUrl;

    /**
     * 商品店铺链接
     */
    private String shopUrl;


    /**
     * 店铺名称
     */
    private String shopName ;

    /**
     * 原始店铺名称
     */
    private String storeName ;

    /**
     * 店铺名称
     */
    private Long shopId ;

    /**
     * 体积
     */
    private String packageSize;

    /**
     * 重量
     */
    private String weight;

    /**
     * 国别
     */
    private String country;

    /**
     * appChannel
     */
    private Integer appChannel;

    /**
     * 全局商品编号
     */
    private String itemNumber;

    /**
     * 所属类目ID
     */
    private Long categoryId;

    /**
     * 类目完整路径
     */
    private String categoryTreeStr;


    /**
     * 原始最高销售价格
     */
    private BigDecimal orginalMaxPrice;

    private String itemCode;

    private String tag;

    //权重字段
    private Long sortValue;

    private Integer isLock;

    private Integer isDel;

    private Integer isReduction;

    private GoodsExtDetailDTO goodsExtDetailModel;

    /**
     * 最低采购价
     */
    private BigDecimal minCostPrice;

    /**
     * 最高采购价
     */
    private BigDecimal maxCostPrice;

    /**
     * 来源供应商
     */
    private String originalShopName;

    /**
     * 国家运费
     */
    private List<CountryDeliveryDto> countryDelivery;

    /**
     * * 1正常  2限流中
     */
    private Integer status;

    /**
     * 商品类型 1表示单品 2表示组合商品 3批发商品
     */
    private Integer type;

    /**
     * 货源数
     */
    private Integer leadTime;

    /**
     * 商品后台扩展信息
     */
    private GoodsBackendInfoDTO backendInfoModel;

    /**
     * 品牌名称
     */
    private String brandName;

}
