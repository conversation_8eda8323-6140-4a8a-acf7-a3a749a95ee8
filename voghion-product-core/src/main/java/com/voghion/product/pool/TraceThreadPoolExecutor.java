package com.voghion.product.pool;

import com.voghion.product.context.TraceContext;
import org.jetbrains.annotations.NotNull;
import org.slf4j.MDC;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2025/4/7 18:17
 */
public class TraceThreadPoolExecutor extends ThreadPoolExecutor {
    private String threadNamePrefix;
    public TraceThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
    }

    public TraceThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue, String namePrefix, RejectedExecutionHandler policy) {
        super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);

        this.threadNamePrefix = namePrefix;

        super.setThreadFactory(new ThreadFactory() {
            private AtomicInteger seq = new AtomicInteger(0);
            @Override
            public Thread newThread(@NotNull Runnable runnable) {
                Thread thread = new Thread(runnable);
                thread.setName(threadNamePrefix + "-" + seq.incrementAndGet());
                return thread;
            }
        });

        super.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Override
    public void execute(Runnable command) {
        Runnable runnable = runnableWarp(command);
        super.execute(runnable);
    }

    private static Runnable runnableWarp(Runnable command) {
        String trace = TraceContext.trace();
        return () -> {
            try {
                TraceContext.traceInit(trace);
                MDC.put(TraceContext.TRACE_ID, trace);
                command.run();
            } catch (Exception e) {
                throw e;
            } finally {
                TraceContext.remove();
                MDC.remove(TraceContext.TRACE_ID);
            }
        };
    }

}
