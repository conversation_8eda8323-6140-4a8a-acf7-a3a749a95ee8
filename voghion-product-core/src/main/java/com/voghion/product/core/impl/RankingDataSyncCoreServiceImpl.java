package com.voghion.product.core.impl;

import com.google.common.collect.Lists;
import com.voghion.es.impl.BaseEsQueryServiceImpl;
import com.voghion.product.pool.TraceThreadPoolExecutor;
import com.voghion.product.util.LogUtils;
import com.voghion.product.core.RankingDataSyncCoreService;
import com.voghion.product.model.dto.RankingDataDTO;
import com.voghion.product.model.enums.RankingOperationTypeEnums;
import com.voghion.product.model.enums.RankingTypeEnums;
import com.voghion.product.model.po.ChanceGoodsTemplate;
import com.voghion.product.model.po.ChanceGoodsTemplateHistory;
import com.voghion.product.model.po.Goods;
import com.voghion.product.service.ChanceGoodsTemplateHistoryService;
import com.voghion.product.service.ChanceGoodsTemplateService;
import com.voghion.product.service.GoodsService;
import com.voghion.product.service.impl.AbstractCommonServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.jetbrains.annotations.NotNull;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.action.search.SearchResponse;
import org.opensearch.client.RequestOptions;
import org.opensearch.client.RestHighLevelClient;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.search.SearchHit;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.opensearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 榜单数据同步核心服务实现类
 * 
 * <AUTHOR>
 * @since 2024-12-19
 */
@Service
@Slf4j
public class RankingDataSyncCoreServiceImpl extends AbstractCommonServiceImpl implements RankingDataSyncCoreService {

    @Resource
    private ChanceGoodsTemplateService chanceGoodsTemplateService;
    @Resource
    private ChanceGoodsTemplateHistoryService chanceGoodsTemplateHistoryService;
    @Resource
    private GoodsService goodsService;
    @Resource
    private BaseEsQueryServiceImpl baseEsQueryServiceImpl;

    /**
     * ES索引名称 - 根据实际项目配置调整
     */
    static final int STATUS_INPROCESS = 4;
    static final int STATUS_DONE = 1;
    static final String RANKING_INDEX = "goods_ranking_index";
    static Map<RankingTypeEnums, BlockingQueue<List<RankingDataDTO>>> blockingQueueMap = new ConcurrentHashMap<>();
    static TraceThreadPoolExecutor rankingSyncMainPool = new TraceThreadPoolExecutor(6, 6, 1, TimeUnit.MINUTES, new SynchronousQueue<>(true), "ranking-main", new ThreadPoolExecutor.CallerRunsPolicy());
    static {
        Arrays.stream(RankingTypeEnums.values()).forEach(rankingTypeEnums -> {
            blockingQueueMap.put(rankingTypeEnums, new ArrayBlockingQueue<>(10000));
        });
    }

    @Override
    public void syncWeeklyRanking() {
        LogUtils.info(log, "开始同步周榜数据");
        try {
            syncRankingByType(RankingTypeEnums.WEEKLY);
            LogUtils.info(log, "周榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "周榜数据同步失败", e);
            throw new RuntimeException("周榜数据同步失败", e);
        }
    }

    @Override
    public void syncMonthlyRanking() {
        LogUtils.info(log, "开始同步月榜数据");
        try {
            syncRankingByType(RankingTypeEnums.MONTHLY);
            LogUtils.info(log, "月榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "月榜数据同步失败", e);
            throw new RuntimeException("月榜数据同步失败", e);
        }
    }

    @Override
    public void syncQuarterlyRanking() {
        LogUtils.info(log, "开始同步季度榜数据");
        try {
            // 同步最近4个季度的榜单
            syncRankingByType(RankingTypeEnums.CURRENT_QUARTER);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_1);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_2);
            syncRankingByType(RankingTypeEnums.LAST_QUARTER_3);
            LogUtils.info(log, "季度榜数据同步完成");
        } catch (Exception e) {
            LogUtils.error(log, "季度榜数据同步失败", e);
            throw new RuntimeException("季度榜数据同步失败", e);
        }
    }

    /**
     * es同步指定类型榜单。
     * @param rankingType
     */
    @Override
    public void syncRankingByType(RankingTypeEnums rankingType) {

        List<CompletableFuture> allFutures = Lists.newArrayList();

        // 1 reader
        allFutures.add(CompletableFuture.runAsync(()-> {
            readRankingDataFromES(rankingType);
        }, rankingSyncMainPool));

        // 4 writer.
        CompletableFuture.runAsync(() -> {
            writeRankingData(rankingType);
        }, rankingSyncMainPool);

    }

    void readRankingDataFromES(RankingTypeEnums rankingType){

        try {
            SearchRequest searchRequest = new SearchRequest(RANKING_INDEX);
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 构建查询条件
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            boolQuery.must(QueryBuilders.termQuery("type", rankingType.getCode()));

            sourceBuilder.query(boolQuery);
            sourceBuilder.sort("rank", SortOrder.ASC); // 按排名升序排列
            sourceBuilder.size(1000); // 限制返回数量

            // 指定返回字段
            sourceBuilder.fetchSource(new String[]{
                    "goods_id", "hot", "rank", "type"
            }, null);

            searchRequest.source(sourceBuilder);
            Pair<List<RankingDataDTO>, String> rankingDataResult = baseEsQueryServiceImpl.searchBatchData(searchRequest, RankingDataDTO.class, null);

            while (rankingDataResult != null) {
                if (CollectionUtils.isEmpty(rankingDataResult.getKey())) {
                    break;
                }
                if (blockingQueueMap.get(rankingType).offer(rankingDataResult.getKey(), 1L, TimeUnit.MINUTES)) {
                    log.info("readRankingDataFromES#offer ranking data to queue success. rankingType:{} , size:{}", rankingType, rankingDataResult.getKey().size());
                } else {
                    log.error("readRankingDataFromES#offer ranking data to queue failed. rankingType:{} , size:{}", rankingType, rankingDataResult.getKey().size());
                    break;
                }
                rankingDataResult = baseEsQueryServiceImpl.searchBatchData(searchRequest, RankingDataDTO.class, rankingDataResult.getRight());
            }
        }catch (InterruptedException e){
            log.error("readRankingDataFromES#batch query ranking es interrupted. rankingType:{} ", rankingType, e);
        }
        catch (Exception e) {
            log.error("readRankingDataFromES#batch query ranking es error. rankingType:{} ", rankingType, e);
        }

    }

    void writeRankingData(RankingTypeEnums rankingType){
        try {
            // 初始化在榜商品为 处理中状态。
            markChanceGoodsTemplate(rankingType, RankingOperationTypeEnums.ENTER.getCode(), STATUS_INPROCESS);

            AtomicInteger count = new AtomicInteger(0);
            // 3 个工作线程
            IntStream.range(0,3)
                    .mapToObj(i -> CompletableFuture.runAsync(() -> consumeRankingDataFromQueue(rankingType, count), rankingSyncMainPool))
                    .forEach(CompletableFuture::join);

            // 处理出榜的商品 mark状态已更新。假设1w以内。
            List<ChanceGoodsTemplate> existGoodsTemplate = chanceGoodsTemplateService.lambdaQuery()
                    .select(ChanceGoodsTemplate::getGoodsId, ChanceGoodsTemplate::getId)
                    .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                    .eq(ChanceGoodsTemplate::getStatus, STATUS_INPROCESS)
                    .eq(ChanceGoodsTemplate::getIsDel, RankingOperationTypeEnums.ENTER.getCode())
                    .list();

            // 处理出榜的商品。 更新状态。
            batchRecordExit(existGoodsTemplate);
            log.info("writeRankingData#existGoodsTemplate: {}", existGoodsTemplate.size());

        }finally {
            markChanceGoodsTemplate(rankingType, RankingOperationTypeEnums.ENTER.getCode(), STATUS_DONE);
        }
    }

    private void consumeRankingDataFromQueue(RankingTypeEnums rankingType, AtomicInteger count){
        try {
            while (true) {
                List<RankingDataDTO> rankingDataList = blockingQueueMap.get(rankingType).poll(1L, TimeUnit.MINUTES);
                if (CollectionUtils.isEmpty(rankingDataList)) {
                    log.info("writeRankingData#empty queue break. rankingType:{}, idx:{}", rankingType, count.getAndIncrement());
                    break;
                }
                if (rankingDataList.size() == 1 && rankingDataList.get(0) == null) {
                    log.info("writeRankingData#writeRankingData done. rankingType:{}, idx:{}", rankingType, count.getAndIncrement());
                    break;
                }
                // 更新当前, mark状态为已更新。
                batchRecordEnter(rankingDataList, rankingType);
                log.info("writeRankingData#syncRankingDataToTemplate and syncRankingDataToHistory success. rankingType:{} , size:{}, idx:{}", rankingType, rankingDataList.size(), count.getAndIncrement());
            }
        }catch (InterruptedException e){
            log.error("writeRankingData#syncRankingData interrupted. rankingType:{} ", rankingType, e);
        }
    }

    private void batchRecordExit(List<ChanceGoodsTemplate> existGoodsTemplate) {
        if (CollectionUtils.isEmpty(existGoodsTemplate)){
            return;
        }

        chanceGoodsTemplateService.updateBatchById(existGoodsTemplate.stream().peek(template -> {
            template.setStatus(STATUS_DONE);
            template.setIsDel(RankingOperationTypeEnums.EXIT.getCode());
            template.setUpdateTime(new Date());
            template.setGoodsId(null); // 不更新该字段。
        }).collect(Collectors.toList()));
    }

    private void markChanceGoodsTemplate(RankingTypeEnums rankingType, Integer code, int statusCode) {
        chanceGoodsTemplateService.lambdaUpdate()
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, code)
                .set(ChanceGoodsTemplate::getStatus, statusCode)
                .set(ChanceGoodsTemplate::getUpdateTime, new Date())
                .update();
    }

    void batchRecordEnter(List<RankingDataDTO> rankingDataList, RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(rankingDataList)) {
            return;
        }

        LogUtils.info(log, "开始同步{}条{}榜单数据到模板表", rankingDataList.size(), rankingType.getName());

        // 1. 获取已存在的商品ID列表
        List<Long> allGoodsIds = rankingDataList.stream()
                .map(RankingDataDTO::getGoodsId)
                .collect(Collectors.toList());
        //      忽略在榜和出榜状态。
        List<ChanceGoodsTemplate> goodsTemplates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getGoodsId, ChanceGoodsTemplate::getId)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .in(ChanceGoodsTemplate::getGoodsId, allGoodsIds)
                .list();
        Set<Long> existingGoodsIds = goodsTemplates.stream().map(ChanceGoodsTemplate::getGoodsId).collect(Collectors.toSet());
        Map<Long, ChanceGoodsTemplate> goodsTemplateMap = goodsTemplates.stream().collect(Collectors.toMap(ChanceGoodsTemplate::getGoodsId, Function.identity(), (v1, v2) -> v1));
        // 2. 批量查询商品信息，避免单条查询
        Map<Long, Goods> goodsMap = batchQueryGoodsMap(allGoodsIds);

        // 3. 批量构建模板数据
        List<ChanceGoodsTemplate> inertTemplateList = new ArrayList<>();
        List<ChanceGoodsTemplate> updateTemplateList = new ArrayList<>();
        Date now = new Date();

        for (RankingDataDTO rankingData : rankingDataList) {
            Goods goods = goodsMap.get(rankingData.getGoodsId());
            if (goods == null) {
                LogUtils.warn(log, "商品ID{}不存在，跳过同步", rankingData.getGoodsId());
                continue;
            }

            if (existingGoodsIds.contains(rankingData.getGoodsId())) {
                ChanceGoodsTemplate old = goodsTemplateMap.get(rankingData.getGoodsId());
                if (Objects.isNull(old)) {
                    log.warn("batchRecordEnter not find is null, goodsId:{}", rankingData.getGoodsId());
                    continue;
                }
                ChanceGoodsTemplate template = buildUpdateChanceGoodsTemplate(rankingData, old, goods, now);
                updateTemplateList.add(template);
            }else {
                ChanceGoodsTemplate template = buildChanceGoodsTemplate(rankingData, goods, rankingType, now);
                inertTemplateList.add(template);
            }
        }

        // 4. 批量保存模板数据
        if (!CollectionUtils.isEmpty(inertTemplateList)) {
            chanceGoodsTemplateService.saveBatch(inertTemplateList);
            LogUtils.info(log, "成功同步{}条{}榜单数据到模板表", inertTemplateList.size(), rankingType.getName());
        }
        if (!CollectionUtils.isEmpty(updateTemplateList)) {
            chanceGoodsTemplateService.updateBatchById(updateTemplateList); // 不能用主键id
            LogUtils.info(log, "成功更新{}条{}榜单数据到模板表", updateTemplateList.size(), rankingType.getName());
        }
    }

    @NotNull
    static ChanceGoodsTemplate buildUpdateChanceGoodsTemplate(RankingDataDTO rankingData, ChanceGoodsTemplate old, Goods goods, Date now) {
        ChanceGoodsTemplate template = new ChanceGoodsTemplate();
        template.setId(old.getId());
        template.setStatus(STATUS_DONE); // 标记已更新。
        template.setHot(rankingData.getHot());
        template.setUpdateTime(now);
        template.setIsDel(RankingOperationTypeEnums.ENTER.getCode()); // 可能出榜状态改成入榜状态。
        template.setGoodsId(null); // no need update.
        // todo: add rank.
        return template;
    }

    @Deprecated
    public void recordRankingHistory(Long templateId, Long goodsId, RankingTypeEnums rankingType,
                                     Integer operationType, Integer rank, Integer hot) {
        try {
            ChanceGoodsTemplateHistory history = new ChanceGoodsTemplateHistory();
            history.setTemplateId(templateId);
            history.setGoodsId(goodsId);
            history.setRankingType(rankingType.getCode());
            history.setOperationType(operationType);
            history.setRank(rank);
            history.setHot(hot);
            history.setOperationTime(new Date());
            history.setCreateTime(new Date());
            history.setUpdateTime(new Date());
            history.setIsDel(0);

            // 获取商品信息补充历史记录
            Goods goods = goodsService.getById(goodsId);
            if (goods != null) {
                history.setGoodsName(goods.getName());
                history.setCategoryId(goods.getCategoryId());
                history.setMainImage(goods.getMainImage());
                history.setMinPrice(goods.getMinPrice());
                history.setMaxPrice(goods.getMaxPrice());
            }

            chanceGoodsTemplateHistoryService.save(history);

            LogUtils.info(log, "记录榜单历史：商品ID={}, 榜单类型={}, 操作类型={}, 排名={}",
                    goodsId, rankingType.getName(), operationType, rank);

        } catch (Exception e) {
            LogUtils.error(log, "记录榜单历史失败：商品ID={}, 榜单类型={}", goodsId, rankingType.getName(), e);
        }
    }


    @Override
    public List<Long> getCurrentRankingGoodsIds(RankingTypeEnums rankingType) {
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getGoodsId)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        return templates.stream()
                .map(ChanceGoodsTemplate::getGoodsId)
                .collect(Collectors.toList());
    }

    public void batchUpdateTemplateInfo(List<RankingDataDTO> rankingDataList) {
        if (CollectionUtils.isEmpty(rankingDataList)) {
            return;
        }

        LogUtils.info(log, "开始批量更新{}条模板信息", rankingDataList.size());

        List<ChanceGoodsTemplate> updateList = new ArrayList<>();

        for (RankingDataDTO rankingData : rankingDataList) {
            ChanceGoodsTemplate template = chanceGoodsTemplateService.lambdaQuery()
                    .eq(ChanceGoodsTemplate::getGoodsId, rankingData.getGoodsId())
                    .eq(ChanceGoodsTemplate::getSourceType, rankingData.getType())
                    .eq(ChanceGoodsTemplate::getIsDel, 0)
                    .one();

            if (template != null) {
                template.setHot(rankingData.getHot());
                template.setSevenSales(rankingData.getSevenSales());
                template.setUpdateTime(new Date());
                template.setUpdateUser("SYSTEM_RANKING_SYNC");
                updateList.add(template);
            }
        }

        if (!CollectionUtils.isEmpty(updateList)) {
            chanceGoodsTemplateService.updateBatchById(updateList);
            LogUtils.info(log, "批量更新{}条模板信息完成", updateList.size());
        }
    }

    /**
     * 批量记录退出榜单的历史
     */
    private void batchRecordExitHistory(List<Long> exitGoodsIds, RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(exitGoodsIds)) {
            return;
        }

        LogUtils.info(log, "批量记录{}个商品退出{}榜单的历史", exitGoodsIds.size(), rankingType.getName());

        // 批量查询模板信息
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getId, ChanceGoodsTemplate::getGoodsId, ChanceGoodsTemplate::getHot)
                .in(ChanceGoodsTemplate::getGoodsId, exitGoodsIds)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(templates)) {
            LogUtils.warn(log, "未找到退出榜单商品的模板信息");
            return;
        }

        // 批量查询商品信息
        Map<Long, Goods> goodsMap = batchQueryGoodsMap(exitGoodsIds);

        // 构建历史记录列表
        List<ChanceGoodsTemplateHistory> historyList = new ArrayList<>();
        Date now = new Date();

        for (ChanceGoodsTemplate template : templates) {
            Goods goods = goodsMap.get(template.getGoodsId());
            ChanceGoodsTemplateHistory history = buildRankingHistory(
                    template.getId(), template.getGoodsId(), goods, rankingType,
                    RankingOperationTypeEnums.EXIT.getCode(), null, template.getHot(), now);
            historyList.add(history);
        }

        // 批量保存历史记录
        chanceGoodsTemplateHistoryService.saveBatch(historyList);
        LogUtils.info(log, "成功批量记录{}条退出榜单历史", historyList.size());
    }

    private void batchRecordEnterHistory(List<RankingDataDTO> newRankingData, RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(newRankingData)){
            return;
        }
        List<Long> enterGoodsIds = newRankingData.stream()
                .map(RankingDataDTO::getGoodsId)
                .collect(Collectors.toList());
        batchRecordEnterHistory(newRankingData, enterGoodsIds, rankingType);
    }
    /**
     * 批量记录进入榜单的历史
     */
    private void batchRecordEnterHistory(List<RankingDataDTO> newRankingData, List<Long> enterGoodsIds,
                                         RankingTypeEnums rankingType) {
        if (CollectionUtils.isEmpty(enterGoodsIds)) {
            return;
        }

        LogUtils.info(log, "批量记录{}个商品进入{}榜单的历史", enterGoodsIds.size(), rankingType.getName());

        // 构建榜单数据映射
        Map<Long, RankingDataDTO> rankingDataMap = newRankingData.stream()
                .collect(Collectors.toMap(RankingDataDTO::getGoodsId, data -> data));

        // 批量查询新创建的模板信息
        List<ChanceGoodsTemplate> templates = chanceGoodsTemplateService.lambdaQuery()
                .select(ChanceGoodsTemplate::getId, ChanceGoodsTemplate::getGoodsId)
                .in(ChanceGoodsTemplate::getGoodsId, enterGoodsIds)
                .eq(ChanceGoodsTemplate::getSourceType, rankingType.getCode())
                .eq(ChanceGoodsTemplate::getIsDel, 0)
                .list();

        if (CollectionUtils.isEmpty(templates)) {
            LogUtils.warn(log, "未找到进入榜单商品的模板信息");
            return;
        }

        // 批量查询商品信息
        Map<Long, Goods> goodsMap = batchQueryGoodsMap(enterGoodsIds);

        // 构建历史记录列表
        List<ChanceGoodsTemplateHistory> historyList = new ArrayList<>();
        Date now = new Date();

        for (ChanceGoodsTemplate template : templates) {
            RankingDataDTO rankingData = rankingDataMap.get(template.getGoodsId());
            Goods goods = goodsMap.get(template.getGoodsId());

            if (rankingData != null && goods != null) {
                ChanceGoodsTemplateHistory history = buildRankingHistory(
                        template.getId(), template.getGoodsId(), goods, rankingType,
                        RankingOperationTypeEnums.ENTER.getCode(),
                        rankingData.getRank(), rankingData.getHot(), now);
                historyList.add(history);
            }
        }

        // 批量保存历史记录
        chanceGoodsTemplateHistoryService.saveBatch(historyList);
        LogUtils.info(log, "成功批量记录{}条进入榜单历史", historyList.size());
    }


    /**
     * 批量查询商品信息并构建Map
     */
    private Map<Long, Goods> batchQueryGoodsMap(List<Long> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return new HashMap<>();
        }

        Collection<Goods> goodsList = goodsService.queryAllGoodsByIds(goodsIds);
        return goodsList.stream()
                .collect(Collectors.toMap(Goods::getId, goods -> goods));
    }

    /**
     * 构建机会商品模板对象
     */
    private ChanceGoodsTemplate buildChanceGoodsTemplate(RankingDataDTO rankingData, Goods goods,
                                                         RankingTypeEnums rankingType, Date now) {
        ChanceGoodsTemplate template = new ChanceGoodsTemplate();
        template.setSourceType(rankingType.getCode());
        template.setSourceDetail(rankingType.getDescription());
        template.setGoodsId(rankingData.getGoodsId());
        template.setGoodsName(goods.getName());
        template.setCategoryId(goods.getCategoryId());
        template.setMainImage(goods.getMainImage());
        template.setHot(rankingData.getHot());
        template.setSevenSales(rankingData.getSevenSales());
        template.setStatus(STATUS_DONE); // 开放状态
        template.setGenerateGoodsCount(0);
        template.setIsDel(0);
        template.setCreateTime(now);
        template.setUpdateTime(now);
        template.setCreateUser("SYSTEM_RANKING_SYNC");
        template.setUpdateUser("SYSTEM_RANKING_SYNC");

        // 设置价格信息
        if (goods.getMinPrice() != null) {
            template.setMinPrice(goods.getMinPrice());
        }
        if (goods.getMaxPrice() != null) {
            template.setMaxPrice(goods.getMaxPrice());
        }

        return template;
    }

    /**
     * 构建榜单历史记录对象
     */
    private ChanceGoodsTemplateHistory buildRankingHistory(Long templateId, Long goodsId, Goods goods,
                                                           RankingTypeEnums rankingType, Integer operationType,
                                                           Integer rank, Integer hot, Date now) {
        ChanceGoodsTemplateHistory history = new ChanceGoodsTemplateHistory();
        history.setTemplateId(templateId);
        history.setGoodsId(goodsId);
        history.setRankingType(rankingType.getCode());
        history.setOperationType(operationType);
        history.setRank(rank);
        history.setHot(hot);
        history.setOperationTime(now);
        history.setCreateTime(now);
        history.setUpdateTime(now);
        history.setIsDel(0);

        // 设置商品信息
        if (goods != null) {
            history.setGoodsName(goods.getName());
            history.setCategoryId(goods.getCategoryId());
            history.setMainImage(goods.getMainImage());
            history.setMinPrice(goods.getMinPrice());
            history.setMaxPrice(goods.getMaxPrice());
        }

        return history;
    }

}
