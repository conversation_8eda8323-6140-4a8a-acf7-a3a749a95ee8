package com.voghion.product.core.impl;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.junit.runner.Runner;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Runner.class)
@Slf4j
class RankingDataSyncCoreServiceImplTest {
    @Resource
    private RankingDataSyncCoreServiceImpl rankingDataSyncCoreService;

    @Test
    void readRankingDataFromES() {

    }
}