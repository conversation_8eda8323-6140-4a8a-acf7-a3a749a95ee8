<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
            http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.voghion</groupId>
        <artifactId>voghion-product</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>voghion-product-core</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-google-storage</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-failover</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba</groupId>-->
<!--            <artifactId>ocean-biz</artifactId>-->
<!--            <version>2.8</version>-->
<!--            <scope>system</scope>-->
<!--            <systemPath>${project.basedir}/lib/ocean.client.java.basic-1.0.6.jar</systemPath>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>ali-ocean-biz</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-model</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>javassist</artifactId>
                    <groupId>org.javassist</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>rocketmq-spring-boot-starter</artifactId>
                    <groupId>org.apache.rocketmq</groupId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-dal</artifactId>
        </dependency>


        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-beanutils</artifactId>
                    <groupId>commons-beanutils</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>poi-ooxml</artifactId>
                    <groupId>org.apache.poi</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.onlest</groupId>
            <artifactId>buss-es-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>dubbo-spring-boot-starter</artifactId>
                    <groupId>org.apache.dubbo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.colorlight</groupId>
            <artifactId>base-model</artifactId>
        </dependency>

        <dependency>
            <groupId>com.colorlight</groupId>
            <artifactId>base-utils</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-logging</artifactId>
                    <groupId>commons-logging</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.colorlight</groupId>
            <artifactId>base-common</artifactId>
        </dependency>


        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-boot-starter-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.colorlight</groupId>
            <artifactId>base-lang</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-product-common</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <!-- -->
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-spring-boot-autoconfigure</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.dubbo</groupId>
            <artifactId>dubbo-dependencies-zookeeper</artifactId>
            <type>pom</type>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>us.codecraft</groupId>
            <artifactId>xsoup</artifactId>
            <version>0.3.2</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>3.2.1</version>
            <exclusions>
                <exclusion>
                    <artifactId>commons-collections4</artifactId>
                    <groupId>org.apache.commons</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-spring-boot-starter</artifactId>
            <version>2.2.2</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>org.springframework</groupId>-->
        <!--            <artifactId>spring-beans</artifactId>-->
        <!--            <version>RELEASE</version>-->
        <!--            <scope>compile</scope>-->
        <!--        </dependency>-->

        <!-- validation -->
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>${validation.api.version}</version>
        </dependency>
        <!-- EL 表达式 -->
        <dependency>
            <groupId>org.glassfish</groupId>
            <artifactId>javax.el</artifactId>
            <version>3.0.1-b11</version>
        </dependency>
        <dependency>
            <groupId>com.amazonaws</groupId>
            <artifactId>aws-java-sdk-core</artifactId>
        </dependency>

        <dependency>
            <groupId>org.opensearch.client</groupId>
            <artifactId>opensearch-rest-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-es</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.8.0</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.0.0</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>4.5.16</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.2</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.3</version>
        </dependency>

        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>alibabacloud-alimt20181012</artifactId>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
        </dependency>

        <dependency>
            <groupId>com.voghion</groupId>
            <artifactId>voghion-google-storage-V2</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.ebay.auth</groupId>
            <artifactId>ebay-oauth-java-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
        </dependency>
        <!--        <dependency>-->
<!--            <groupId>org.springframework.cloud</groupId>-->
<!--            <artifactId>spring-cloud-context</artifactId>-->
<!--            <version>2.1.6.RELEASE</version>-->
<!--        </dependency>-->
    </dependencies>
</project>