<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.voghion</groupId>
        <artifactId>voghion-boot-parent</artifactId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <groupId>com.voghion</groupId>
    <artifactId>voghion-product</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>voghion-product</name>

    <properties>
        <api.version>1.0.3-SNAPSHOT</api.version>
        <validation.api.version>2.0.1.Final</validation.api.version>
        <javax.el.version>3.0.1-b11</javax.el.version>
        <cloud.alibaba.nacos.version>0.2.1.RELEASE</cloud.alibaba.nacos.version>
        <mapstruct.version>1.6.0.Beta1</mapstruct.version>

        <voghion.boot.version>1.0.14-SNAPSHOT</voghion.boot.version>
    </properties>

    <!--项目子模块 -->
    <modules>
        <module>voghion-product-common</module>
        <module>voghion-product-dal</module>
        <module>voghion-product-service</module>
        <module>voghion-product-core</module>
        <module>voghion-product-api</module>
        <module>voghion-product-model</module>
        <module>voghion-product-admin</module>
        <module>voghion-product-client</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-boot-dependencies</artifactId>
                <version>${voghion.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!--mysql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>8.0.11</version>
            </dependency>

            <!--mybatisplus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-generator</artifactId>
                <version>3.1.2</version>
            </dependency>

            <dependency>
                <groupId>com.lmax</groupId>
                <artifactId>disruptor</artifactId>
                <version>3.4.2</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>2.0.27</version>
            </dependency>

            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.14</version>
                <scope>provided</scope>
            </dependency>
            <!-- <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>2.0</version>
            </dependency>

            <dependency>
                <groupId>org.freemarker</groupId>
                <artifactId>freemarker</artifactId>
                <version>2.3.23</version>
            </dependency> -->

            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.8</version>
            </dependency>

            <!--swagger2 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.8.0</version>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.8.0</version>
            </dependency>

            <dependency>
                <groupId>javax.xml.bind</groupId>
                <artifactId>jaxb-api</artifactId>
                <version>2.3.0</version>
            </dependency>

            <!-- bases -->
            <dependency>
                <groupId>com.colorlight</groupId>
                <artifactId>base-common</artifactId>
                <version>0.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.colorlight</groupId>
                <artifactId>base-lang</artifactId>
                <version>0.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.colorlight</groupId>
                <artifactId>base-model</artifactId>
                <version>0.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.colorlight</groupId>
                <artifactId>base-mybatis</artifactId>
                <version>0.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.colorlight</groupId>
                <artifactId>base-utils</artifactId>
                <version>0.0.1</version>
            </dependency>

            <!--modules -->
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-common</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-dal</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-api</artifactId>
                <version>${api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-service</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-client</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-model</artifactId>
                <version>${api.version}</version>
            </dependency>

            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-product-core</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>javax.validation</groupId>
                <artifactId>validation-api</artifactId>
                <version>${validation.api.version}</version>
            </dependency>
            <dependency>
                <groupId>org.glassfish</groupId>
                <artifactId>javax.el</artifactId>
                <version>${javax.el.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-cache</artifactId>
                <version>2.6.3</version>
            </dependency>
            <dependency>
                <groupId>com.github.ben-manes.caffeine</groupId>
                <artifactId>caffeine</artifactId>
                <version>2.6.0</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-secretsmanager</artifactId>
                <version>1.11.965</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk</artifactId>
                <version>1.11.965</version>
            </dependency>
            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-core</artifactId>
                <version>1.11.965</version>
            </dependency>
            <dependency>
                <groupId>org.opensearch.client</groupId>
                <artifactId>opensearch-rest-high-level-client</artifactId>
                <version>1.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.apache.shardingsphere</groupId>
                <artifactId>shardingsphere-jdbc-core-spring-boot-starter</artifactId>
                <version>5.2.1</version>
            </dependency>
            <dependency>
                <groupId>org.yaml</groupId>
                <artifactId>snakeyaml</artifactId>
                <version>1.33</version>
            </dependency>

            <!--            <dependency>-->
<!--                <groupId>com.alibaba</groupId>-->
<!--                <artifactId>druid-spring-boot-starter</artifactId>-->
<!--                <version>1.2.8</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.alibaba</groupId>-->
<!--                <artifactId>druid</artifactId>-->
<!--                <version>1.1.22</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>org.apache.shardingsphere</groupId>-->
<!--                <artifactId>sharding-jdbc-spring-boot-starter</artifactId>-->
<!--                <version>4.0.0-RC1</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>3.2.1</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-mq</artifactId>
                <version>0.0.2-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-es</artifactId>
                <version>3.0.68-RELEASE</version>
                <!--<version>3.0.59-SNAPSHOT</version>-->
            </dependency>
            <dependency>
                <groupId>com.onlest</groupId>
                <artifactId>buss-es-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>

                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>alibabacloud-alimt20181012</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-merchant-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.voghion</groupId>
                <artifactId>voghion-backend-api</artifactId>
                <version>1.0.0-SNAPSHOT</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>alibaba-open-sdk</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>ali-ocean-biz</artifactId>
                <version>1.0.0</version>
            </dependency>

            <dependency>
                <groupId>org.sejda.imageio</groupId>
                <artifactId>webp-imageio</artifactId>
                <version>0.1.6</version>
            </dependency>
            <dependency>
                <groupId>com.opencsv</groupId>
                <artifactId>opencsv</artifactId>
                <version>5.5.2</version>
            </dependency>

            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>2.3.0</version>
            </dependency>

            <dependency>
                <groupId>com.ebay.auth</groupId>
                <artifactId>ebay-oauth-java-client</artifactId>
                <version>1.1.10-RELEASE</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>asm</artifactId>
                    <groupId>org.ow2.asm</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <!--多环境配置 -->
    <profiles>
        <!--本地 -->
        <profile>
            <id>dev</id>
            <properties>
                <env>dev</env>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试 -->
        <profile>
            <id>test</id>
            <properties>
                <env>test</env>
            </properties>
        </profile>
        <!--预发 -->
        <profile>
            <id>pre</id>
            <properties>
                <env>pre</env>
            </properties>
        </profile>
        <!--生产 -->
        <profile>
            <id>prod</id>
            <properties>
                <env>prod</env>
            </properties>
        </profile>
    </profiles>

    <!-- <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <includes>
                    <include>**/*.xml</include>
                    <include>**/*.properties</include>
                </includes>
                <filtering>true</filtering>
            </resource>
        </resources>
    </build> -->
</project>
